

# Python 3.9 kontrolü (py launcher ile)
function Check-Python39 {
    try {
        $python39VersionOutput = py -3.9 --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Python 3.9 bulundu: $python39VersionOutput"
            return $true
        } else {
            Write-Host "Python 3.9 bulunamadı."
            return $false
        }
    } catch {
        Write-Host "py launcher veya Python 3.9 bulunamadı."
        return $false
    }
}

function Install-Python39 {
    Write-Host "Python 3.9 yükleniyor..."
    & "$PSScriptRoot\install_python39.ps1"

    # Kurulumdan sonra tekrar kontrol et
    $python39VersionOutput = py -3.9 --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python 3.9 başarıyla kuruldu: $python39VersionOutput"
        return $true
    } else {
        Write-Host "HATA: Python 3.9 kurulumu başarısız!"
        Pause
        exit 1
    }
}

# Ana akış
if (-not (Check-Python39)) {
    if (-not (Install-Python39)) {
        Write-Host "HATA: Python 3.9 kurulumu başarısız!"
        Pause
        exit 1
    }
}

# Python 3.9 path'i
$python39Path = "py -3.9"