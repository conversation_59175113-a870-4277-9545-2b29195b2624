# Sanal ortam klasör adini belirle
$VENV_DIR = "venv"

<#
# Python yuklu mu kontrol et
try {
    python --version > $null 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Python yuklu degil veya PATH'e ekli degil."
        Pause
        exit 1
    }
} catch {
    Write-Host "Python yuklu degil veya PATH'e ekli degil."
    Pause
    exit 1
}
#>

# Python yuklu mu kontrol et
try {
    $pythonVersionOutput = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Python yuklu değil veya PATH'e ekli değil."
        Pause
        exit 1
    } else {
        Write-Host "Bulunan Python surumu: $pythonVersionOutput"

        # İstersen minimum surum kontrolu yapabilirsin:
        # Örnek: Minimum Python 3.7 gerekiyor
        $minVersion = [version]"3.7.0"

        # python --version çıktısı genelde "Python X.Y.Z" formatında
        $versionString = $pythonVersionOutput -replace "Python ", ""
        $currentVersion = [version]$versionString

        if ($currentVersion -lt $minVersion) {
            Write-Host "Python surumu çok eski. En az $minVersion surumu gerekiyor."
            Pause
            exit 1
        }
    }
}
catch {
    Write-Host "Python yuklu değil veya PATH'e ekli değil."
    Pause
    exit 1
}


# Sanal ortam olustur
if (-not (Test-Path $VENV_DIR)) {
    Write-Host "Sanal ortam olusturuluyor..."
    python -m venv $VENV_DIR
} else {
    Write-Host "Sanal ortam zaten var: $VENV_DIR"
}

# Ortami aktive et
$activateScript = Join-Path $VENV_DIR "Scripts\Activate.ps1"
if (Test-Path $activateScript) {
    & $activateScript
    Write-Host "Sanal ortam aktif edildi."
} else {
    Write-Host "Aktivasyon dosyasi bulunamadi: $activateScript"
    Pause
    exit 1
}

# Ana bagimliliklari yukle
if (Test-Path "requirements.txt") {
    Write-Host "requirements.txt yukleniyor..."
    pip install -r requirements.txt
} else {
    Write-Host "requirements.txt bulunamadi."
}

# Gelistirme bagimliliklari istege bagli
if (Test-Path "requirements-dev.txt") {
    $DEVINSTALL = Read-Host "Gelistirme bagimliliklari yuklensin mi? (E/H)"
    if ($DEVINSTALL -ieq "E") {
        pip install -r requirements-dev.txt
    } else {
        Write-Host "Gelistirme bagimliliklari atlandi."
    }
} else {
    Write-Host "requirements-dev.txt bulunamadi."
}

Write-Host ""
Write-Host "Ortam hazir! Sanal ortam aktif."
Write-Host ""
Write-Host "Komut satirinda isiniz bitince 'deactivate' yazabilirsiniz."
Pause